#!/usr/bin/env python3
"""
PDF Splitter Script for Global People Austria Directory

This script traverses the Global People Austria directory and all its subfolders,
finds PDF files, splits them into individual pages, and stores them in a new folder structure.
"""

import os
import sys
from pathlib import Path
from PyPDF2 import PdfReader, PdfWriter
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pdf_splitter.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


def create_output_directory(base_output_dir):
    """Create the base output directory if it doesn't exist."""
    output_path = Path(base_output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    return output_path


def get_relative_path(file_path, base_dir):
    """Get the relative path from the base directory."""
    return Path(file_path).relative_to(Path(base_dir))


def split_pdf(pdf_path, output_dir):
    """
    Split a PDF file into individual pages and save them in the output directory.
    
    Args:
        pdf_path (Path): Path to the PDF file to split
        output_dir (Path): Directory where split pages will be saved
    
    Returns:
        int: Number of pages split, or -1 if error occurred
    """
    try:
        # Read the PDF
        with open(pdf_path, 'rb') as pdf_file:
            pdf_reader = PdfReader(pdf_file)
            num_pages = len(pdf_reader.pages)
            
            logger.info(f"Splitting {pdf_path.name} ({num_pages} pages)")
            
            # Create output directory for this PDF
            pdf_output_dir = output_dir / pdf_path.stem
            pdf_output_dir.mkdir(parents=True, exist_ok=True)
            
            # Split each page
            for page_num in range(num_pages):
                # Create a new PDF writer for this page
                pdf_writer = PdfWriter()
                pdf_writer.add_page(pdf_reader.pages[page_num])
                
                # Create output filename
                page_filename = f"{pdf_path.stem}_page_{page_num + 1:03d}.pdf"
                page_output_path = pdf_output_dir / page_filename
                
                # Write the page to a new PDF file
                with open(page_output_path, 'wb') as output_file:
                    pdf_writer.write(output_file)
                
                logger.debug(f"Created: {page_output_path}")
            
            logger.info(f"Successfully split {pdf_path.name} into {num_pages} pages")
            return num_pages
            
    except Exception as e:
        logger.error(f"Error splitting {pdf_path}: {str(e)}")
        return -1


def traverse_and_split_pdfs(source_dir, output_base_dir):
    """
    Traverse the source directory recursively and split all PDF files.
    
    Args:
        source_dir (str): Path to the source directory to traverse
        output_base_dir (str): Base directory for output files
    """
    source_path = Path(source_dir)
    output_path = Path(output_base_dir)
    
    if not source_path.exists():
        logger.error(f"Source directory does not exist: {source_dir}")
        return
    
    # Create base output directory
    create_output_directory(output_base_dir)
    
    # Statistics
    total_pdfs = 0
    total_pages = 0
    failed_pdfs = 0
    
    logger.info(f"Starting PDF splitting process...")
    logger.info(f"Source directory: {source_path.absolute()}")
    logger.info(f"Output directory: {output_path.absolute()}")
    
    # Walk through all directories and subdirectories
    for root, dirs, files in os.walk(source_path):
        root_path = Path(root)
        
        # Find all PDF files in current directory
        pdf_files = [f for f in files if f.lower().endswith('.pdf')]
        
        if pdf_files:
            logger.info(f"Found {len(pdf_files)} PDF(s) in: {root_path}")
            
            # Create corresponding output directory structure
            relative_path = root_path.relative_to(source_path)
            current_output_dir = output_path / relative_path
            current_output_dir.mkdir(parents=True, exist_ok=True)
            
            # Process each PDF file
            for pdf_file in pdf_files:
                pdf_path = root_path / pdf_file
                total_pdfs += 1
                
                logger.info(f"Processing PDF {total_pdfs}: {pdf_path}")
                
                # Split the PDF
                pages_split = split_pdf(pdf_path, current_output_dir)
                
                if pages_split > 0:
                    total_pages += pages_split
                else:
                    failed_pdfs += 1
    
    # Print summary
    logger.info("=" * 60)
    logger.info("PDF SPLITTING SUMMARY")
    logger.info("=" * 60)
    logger.info(f"Total PDFs processed: {total_pdfs}")
    logger.info(f"Total pages created: {total_pages}")
    logger.info(f"Failed PDFs: {failed_pdfs}")
    logger.info(f"Success rate: {((total_pdfs - failed_pdfs) / total_pdfs * 100):.1f}%" if total_pdfs > 0 else "N/A")
    logger.info(f"Output directory: {output_path.absolute()}")


def main():
    """Main function to run the PDF splitter."""
    # Configuration
    source_directory = "Global People Austria"
    output_directory = "Global People Austria - Split Pages"
    
    logger.info("PDF Splitter Script Starting...")
    
    # Check if source directory exists
    if not os.path.exists(source_directory):
        logger.error(f"Source directory '{source_directory}' not found!")
        logger.error("Please make sure you're running this script from the correct directory.")
        return 1
    
    try:
        # Start the splitting process
        traverse_and_split_pdfs(source_directory, output_directory)
        logger.info("PDF splitting process completed successfully!")
        return 0
        
    except KeyboardInterrupt:
        logger.info("Process interrupted by user.")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
