#!/usr/bin/env python3
"""
PDF Splitter Script for Global People Directories

This script traverses directories containing Global People data and all their subfolders,
finds PDF files, splits them into individual pages, stores them in a new folder structure,
and creates a JSON directory with metadata for each page.
"""

import os
import sys
import json
from pathlib import Path
from PyPDF2 import PdfReader, PdfWriter
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pdf_splitter.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)


def create_output_directory(base_output_dir):
    """Create the base output directory if it doesn't exist."""
    output_path = Path(base_output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    return output_path


def get_relative_path(file_path, base_dir):
    """Get the relative path from the base directory."""
    return Path(file_path).relative_to(Path(base_dir))


def extract_country_from_path(pdf_path, source_dir):
    """
    Extract country from the directory path.
    Assumes the country is in the directory name (e.g., 'Global People Austria' -> 'Austria')
    """
    try:
        # Get the relative path from source directory
        relative_path = pdf_path.relative_to(Path(source_dir).parent)
        # Get the first directory component which should contain the country
        first_dir = relative_path.parts[0]
        # Extract country from directory name (e.g., 'Global People Austria' -> 'Austria')
        if 'Global People' in first_dir:
            country = first_dir.replace('Global People', '').strip()
            return country if country else 'Unknown'
        return 'Unknown'
    except Exception:
        return 'Unknown'


def split_pdf(pdf_path, output_dir, source_dir, json_entries):
    """
    Split a PDF file into individual pages and save them in the output directory.
    Also creates JSON entries for each page.

    Args:
        pdf_path (Path): Path to the PDF file to split
        output_dir (Path): Directory where split pages will be saved
        source_dir (str): Source directory path for country extraction
        json_entries (list): List to store JSON entries for each page

    Returns:
        int: Number of pages split, or -1 if error occurred
    """
    try:
        # Read the PDF
        with open(pdf_path, 'rb') as pdf_file:
            pdf_reader = PdfReader(pdf_file)
            num_pages = len(pdf_reader.pages)

            logger.info(f"Splitting {pdf_path.name} ({num_pages} pages)")

            # Create output directory for this PDF
            pdf_output_dir = output_dir / pdf_path.stem
            pdf_output_dir.mkdir(parents=True, exist_ok=True)

            # Extract country from path
            country = extract_country_from_path(pdf_path, source_dir)

            # Split each page
            for page_num in range(num_pages):
                # Create a new PDF writer for this page
                pdf_writer = PdfWriter()
                pdf_writer.add_page(pdf_reader.pages[page_num])

                # Create output filename
                page_filename = f"{pdf_path.stem}_page_{page_num + 1:03d}.pdf"
                page_output_path = pdf_output_dir / page_filename

                # Write the page to a new PDF file
                with open(page_output_path, 'wb') as output_file:
                    pdf_writer.write(output_file)

                # Create JSON entry for this page
                json_entry = {
                    "filename": page_filename,
                    "file_path": str(page_output_path.absolute()),
                    "country": country,
                    "icp": "Global People"
                }
                json_entries.append(json_entry)

                logger.debug(f"Created: {page_output_path}")

            logger.info(f"Successfully split {pdf_path.name} into {num_pages} pages")
            return num_pages

    except Exception as e:
        logger.error(f"Error splitting {pdf_path}: {str(e)}")
        return -1


def traverse_and_split_pdfs(source_dir, output_base_dir):
    """
    Traverse the source directory recursively and split all PDF files.
    Creates a JSON directory with metadata for each page.

    Args:
        source_dir (str): Path to the source directory to traverse
        output_base_dir (str): Base directory for output files

    Returns:
        str: Path to the created JSON directory file
    """
    source_path = Path(source_dir)
    output_path = Path(output_base_dir)

    if not source_path.exists():
        logger.error(f"Source directory does not exist: {source_dir}")
        return None

    # Create base output directory
    create_output_directory(output_base_dir)

    # Statistics
    total_pdfs = 0
    total_pages = 0
    failed_pdfs = 0

    # JSON entries list
    json_entries = []

    logger.info(f"Starting PDF splitting process...")
    logger.info(f"Source directory: {source_path.absolute()}")
    logger.info(f"Output directory: {output_path.absolute()}")

    # Walk through all directories and subdirectories
    for root, _, files in os.walk(source_path):
        root_path = Path(root)

        # Find all PDF files in current directory
        pdf_files = [f for f in files if f.lower().endswith('.pdf')]

        if pdf_files:
            logger.info(f"Found {len(pdf_files)} PDF(s) in: {root_path}")

            # Create corresponding output directory structure
            relative_path = root_path.relative_to(source_path)
            current_output_dir = output_path / relative_path
            current_output_dir.mkdir(parents=True, exist_ok=True)

            # Process each PDF file
            for pdf_file in pdf_files:
                pdf_path = root_path / pdf_file
                total_pdfs += 1

                logger.info(f"Processing PDF {total_pdfs}: {pdf_path}")

                # Split the PDF
                pages_split = split_pdf(pdf_path, current_output_dir, source_dir, json_entries)

                if pages_split > 0:
                    total_pages += pages_split
                else:
                    failed_pdfs += 1
    
    # Create JSON directory file
    json_filename = f"pdf_directory_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    json_file_path = output_path / json_filename

    try:
        with open(json_file_path, 'w', encoding='utf-8') as json_file:
            json.dump({
                "metadata": {
                    "created_at": datetime.now().isoformat(),
                    "source_directory": str(source_path.absolute()),
                    "output_directory": str(output_path.absolute()),
                    "total_pdfs_processed": total_pdfs,
                    "total_pages_created": total_pages,
                    "failed_pdfs": failed_pdfs
                },
                "pages": json_entries
            }, json_file, indent=2, ensure_ascii=False)

        logger.info(f"JSON directory created: {json_file_path}")
    except Exception as e:
        logger.error(f"Error creating JSON directory: {str(e)}")

    # Print summary
    logger.info("=" * 60)
    logger.info("PDF SPLITTING SUMMARY")
    logger.info("=" * 60)
    logger.info(f"Total PDFs processed: {total_pdfs}")
    logger.info(f"Total pages created: {total_pages}")
    logger.info(f"Failed PDFs: {failed_pdfs}")
    logger.info(f"Success rate: {((total_pdfs - failed_pdfs) / total_pdfs * 100):.1f}%" if total_pdfs > 0 else "N/A")
    logger.info(f"Output directory: {output_path.absolute()}")
    logger.info(f"JSON directory: {json_file_path}")

    return str(json_file_path)


def find_global_people_directories():
    """Find all directories that start with 'Global People'."""
    current_dir = Path(".")
    global_people_dirs = []

    for item in current_dir.iterdir():
        if item.is_dir() and item.name.startswith("Global People"):
            global_people_dirs.append(item.name)

    return global_people_dirs


def main():
    """Main function to run the PDF splitter."""
    logger.info("PDF Splitter Script Starting...")

    # Find all Global People directories
    global_people_dirs = find_global_people_directories()

    if not global_people_dirs:
        logger.error("No 'Global People' directories found!")
        logger.error("Please make sure you have directories starting with 'Global People' in the current directory.")
        return 1

    logger.info(f"Found {len(global_people_dirs)} Global People director{'y' if len(global_people_dirs) == 1 else 'ies'}:")
    for dir_name in global_people_dirs:
        logger.info(f"  - {dir_name}")

    try:
        json_files_created = []

        # Process each Global People directory
        for source_directory in global_people_dirs:
            logger.info(f"\n{'='*60}")
            logger.info(f"PROCESSING: {source_directory}")
            logger.info(f"{'='*60}")

            output_directory = f"{source_directory} - Split Pages"

            # Start the splitting process
            json_file_path = traverse_and_split_pdfs(source_directory, output_directory)
            if json_file_path:
                json_files_created.append(json_file_path)

        logger.info(f"\n{'='*60}")
        logger.info("ALL PROCESSING COMPLETED!")
        logger.info(f"{'='*60}")
        logger.info(f"Processed {len(global_people_dirs)} director{'y' if len(global_people_dirs) == 1 else 'ies'}")
        logger.info("JSON directory files created:")
        for json_file in json_files_created:
            logger.info(f"  - {json_file}")

        return 0

    except KeyboardInterrupt:
        logger.info("Process interrupted by user.")
        return 1
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
