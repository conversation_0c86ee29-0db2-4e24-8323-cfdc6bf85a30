# PDF Splitter for Global People Directories

This script automatically finds and processes all directories starting with "Global People" (e.g., "Global People Austria", "Global People Germany", etc.), traverses all their subfolders, finds PDF files, splits them into individual pages, and stores them in organized folder structures. It also creates a comprehensive JSON directory with metadata for each page.

## Features

- **Auto-Discovery**: Automatically finds all "Global People" directories in the current folder
- **Multi-Directory Processing**: Processes all Global People directories in one run
- **Recursive Directory Traversal**: Automatically finds all PDF files in subdirectories
- **Page-by-Page Splitting**: Each PDF page becomes a separate PDF file
- **JSON Directory**: Creates a comprehensive JSON file with metadata for each page
- **Organized Output Structure**: Maintains the original directory structure in the output
- **Detailed Logging**: Comprehensive logging to both console and log file
- **Error Handling**: Graceful handling of corrupted or problematic PDF files
- **Progress Tracking**: Shows processing progress and final statistics

## Requirements

- Python 3.6 or higher
- PyPDF2 library

## Installation

1. Make sure you have Python installed
2. Install the required dependencies:

```bash
pip install -r requirements.txt
```

Or install PyPDF2 directly:

```bash
pip install PyPDF2
```

## Usage

1. Place the script in the same directory that contains your "Global People" folders (e.g., "Global People Austria", "Global People Germany", etc.)
2. Run the script:

```bash
python pdf_splitter.py
```

The script will automatically detect and process all directories starting with "Global People".

## Output Structure

For each Global People directory, the script will create a corresponding "Split Pages" directory. For example, "Global People Austria" will create "Global People Austria - Split Pages" with the following structure:

```
Global People Austria - Split Pages/
├── 0125/
│   ├── CEPI/
│   │   ├── Expense AUT NB Jan 25 2 signed/
│   │   │   ├── Expense AUT NB Jan 25 2 signed_page_001.pdf
│   │   │   ├── Expense AUT NB Jan 25 2 signed_page_002.pdf
│   │   │   └── ...
│   │   └── Expense AUT NB Jan 25 3 signed/
│   │       ├── Expense AUT NB Jan 25 3 signed_page_001.pdf
│   │       └── ...
│   └── Checkmarx/
│       └── ...
└── 0225/
    └── ...
```

## JSON Directory

For each processed directory, the script creates a comprehensive JSON file named `pdf_directory_YYYYMMDD_HHMMSS.json` containing:

### Metadata Section
- Processing timestamp
- Source and output directory paths
- Processing statistics (total PDFs, pages created, failed PDFs)

### Pages Section
Each PDF page entry contains:
- **filename**: Name of the split page file (e.g., "document_page_001.pdf")
- **file_path**: Absolute path to the split page file
- **country**: Extracted from the directory name (e.g., "Austria" from "Global People Austria")
- **icp**: Always set to "Global People"

### Example JSON Structure
```json
{
  "metadata": {
    "created_at": "2025-01-18T10:30:15.123456",
    "source_directory": "D:\\RGT\\Papaya real dataset\\Global People Austria",
    "output_directory": "D:\\RGT\\Papaya real dataset\\Global People Austria - Split Pages",
    "total_pdfs_processed": 15,
    "total_pages_created": 45,
    "failed_pdfs": 0
  },
  "pages": [
    {
      "filename": "Expense AUT NB Jan 25 2 signed_page_001.pdf",
      "file_path": "D:\\RGT\\Papaya real dataset\\Global People Austria - Split Pages\\0125\\CEPI\\Expense AUT NB Jan 25 2 signed\\Expense AUT NB Jan 25 2 signed_page_001.pdf",
      "country": "Austria",
      "icp": "Global People"
    },
    {
      "filename": "Expense AUT NB Jan 25 2 signed_page_002.pdf",
      "file_path": "D:\\RGT\\Papaya real dataset\\Global People Austria - Split Pages\\0125\\CEPI\\Expense AUT NB Jan 25 2 signed\\Expense AUT NB Jan 25 2 signed_page_002.pdf",
      "country": "Austria",
      "icp": "Global People"
    }
  ]
}
```

## Logging

The script creates a log file called `pdf_splitter.log` that contains detailed information about the processing, including:

- Which files are being processed
- Number of pages in each PDF
- Any errors encountered
- Final statistics

## Error Handling

- If a PDF file is corrupted or cannot be read, the script will log the error and continue with the next file
- The script maintains a count of failed PDFs and reports this in the final summary

## Example Output

```
2025-01-18 10:30:15 - INFO - PDF Splitter Script Starting...
2025-01-18 10:30:15 - INFO - Found 2 Global People directories:
2025-01-18 10:30:15 - INFO -   - Global People Austria
2025-01-18 10:30:15 - INFO -   - Global People Germany

============================================================
PROCESSING: Global People Austria
============================================================
2025-01-18 10:30:15 - INFO - Starting PDF splitting process...
2025-01-18 10:30:15 - INFO - Source directory: D:\RGT\Papaya real dataset\Global People Austria
2025-01-18 10:30:15 - INFO - Output directory: D:\RGT\Papaya real dataset\Global People Austria - Split Pages
2025-01-18 10:30:15 - INFO - Found 5 PDF(s) in: Global People Austria\0125\CEPI
2025-01-18 10:30:15 - INFO - Processing PDF 1: Global People Austria\0125\CEPI\Expense AUT NB Jan 25 2 signed.pdf
2025-01-18 10:30:15 - INFO - Splitting Expense AUT NB Jan 25 2 signed.pdf (3 pages)
2025-01-18 10:30:15 - INFO - Successfully split Expense AUT NB Jan 25 2 signed.pdf into 3 pages
...
2025-01-18 10:30:20 - INFO - JSON directory created: D:\RGT\Papaya real dataset\Global People Austria - Split Pages\pdf_directory_20250118_103020.json
============================================================
PDF SPLITTING SUMMARY
============================================================
Total PDFs processed: 15
Total pages created: 45
Failed PDFs: 0
Success rate: 100.0%
Output directory: D:\RGT\Papaya real dataset\Global People Austria - Split Pages
JSON directory: D:\RGT\Papaya real dataset\Global People Austria - Split Pages\pdf_directory_20250118_103020.json

============================================================
PROCESSING: Global People Germany
============================================================
...

============================================================
ALL PROCESSING COMPLETED!
============================================================
Processed 2 directories
JSON directory files created:
  - D:\RGT\Papaya real dataset\Global People Austria - Split Pages\pdf_directory_20250118_103020.json
  - D:\RGT\Papaya real dataset\Global People Germany - Split Pages\pdf_directory_20250118_103025.json
```

## Troubleshooting

1. **"No 'Global People' directories found" error**: Make sure you're running the script from the directory that contains folders starting with "Global People" (e.g., "Global People Austria", "Global People Germany")

2. **Permission errors**: Ensure you have write permissions in the directory where you're running the script

3. **Memory issues with large PDFs**: The script loads entire PDFs into memory. For very large PDFs, you might need to increase available memory or process them separately

4. **PyPDF2 import error**: Make sure PyPDF2 is installed correctly with `pip install PyPDF2`

5. **JSON encoding issues**: The script uses UTF-8 encoding for JSON files. If you encounter encoding issues with special characters in filenames, check your system's locale settings

## Customization

You can modify the script to change:

- Directory detection pattern (modify the `startswith("Global People")` condition in `find_global_people_directories()`)
- Output directory naming (change the `f"{source_directory} - Split Pages"` format in `main()`)
- JSON filename format (modify `json_filename` creation in `traverse_and_split_pdfs()`)
- Logging level (change `level=logging.INFO` to `level=logging.DEBUG` for more detailed logs)
- Output filename format (modify the `page_filename` creation in `split_pdf()` function)
- JSON metadata fields (add or modify fields in the `json_entry` dictionary in `split_pdf()`)
- Country extraction logic (modify the `extract_country_from_path()` function for different naming conventions)
