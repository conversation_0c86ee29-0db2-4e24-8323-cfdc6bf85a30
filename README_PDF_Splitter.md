# PDF Splitter for Global People Austria Directory

This script traverses the "Global People Austria" directory and all its subfolders, finds PDF files, splits them into individual pages, and stores them in a new organized folder structure.

## Features

- **Recursive Directory Traversal**: Automatically finds all PDF files in subdirectories
- **Page-by-Page Splitting**: Each PDF page becomes a separate PDF file
- **Organized Output Structure**: Maintains the original directory structure in the output
- **Detailed Logging**: Comprehensive logging to both console and log file
- **Error Handling**: Graceful handling of corrupted or problematic PDF files
- **Progress Tracking**: Shows processing progress and final statistics

## Requirements

- Python 3.6 or higher
- PyPDF2 library

## Installation

1. Make sure you have Python installed
2. Install the required dependencies:

```bash
pip install -r requirements.txt
```

Or install PyPDF2 directly:

```bash
pip install PyPDF2
```

## Usage

1. Place the script in the same directory that contains the "Global People Austria" folder
2. Run the script:

```bash
python pdf_splitter.py
```

## Output Structure

The script will create a new directory called "Global People Austria - Split Pages" with the following structure:

```
Global People Austria - Split Pages/
├── 0125/
│   ├── CEPI/
│   │   ├── Expense AUT NB Jan 25 2 signed/
│   │   │   ├── Expense AUT NB Jan 25 2 signed_page_001.pdf
│   │   │   ├── Expense AUT NB Jan 25 2 signed_page_002.pdf
│   │   │   └── ...
│   │   └── Expense AUT NB Jan 25 3 signed/
│   │       ├── Expense AUT NB Jan 25 3 signed_page_001.pdf
│   │       └── ...
│   └── Checkmarx/
│       └── ...
└── 0225/
    └── ...
```

## Logging

The script creates a log file called `pdf_splitter.log` that contains detailed information about the processing, including:

- Which files are being processed
- Number of pages in each PDF
- Any errors encountered
- Final statistics

## Error Handling

- If a PDF file is corrupted or cannot be read, the script will log the error and continue with the next file
- The script maintains a count of failed PDFs and reports this in the final summary

## Example Output

```
2025-01-18 10:30:15 - INFO - PDF Splitter Script Starting...
2025-01-18 10:30:15 - INFO - Starting PDF splitting process...
2025-01-18 10:30:15 - INFO - Source directory: D:\RGT\Papaya real dataset\Global People Austria
2025-01-18 10:30:15 - INFO - Output directory: D:\RGT\Papaya real dataset\Global People Austria - Split Pages
2025-01-18 10:30:15 - INFO - Found 5 PDF(s) in: Global People Austria\0125\CEPI
2025-01-18 10:30:15 - INFO - Processing PDF 1: Global People Austria\0125\CEPI\Expense AUT NB Jan 25 2 signed.pdf
2025-01-18 10:30:15 - INFO - Splitting Expense AUT NB Jan 25 2 signed.pdf (3 pages)
2025-01-18 10:30:15 - INFO - Successfully split Expense AUT NB Jan 25 2 signed.pdf into 3 pages
...
============================================================
PDF SPLITTING SUMMARY
============================================================
Total PDFs processed: 15
Total pages created: 45
Failed PDFs: 0
Success rate: 100.0%
Output directory: D:\RGT\Papaya real dataset\Global People Austria - Split Pages
```

## Troubleshooting

1. **"Source directory not found" error**: Make sure you're running the script from the directory that contains the "Global People Austria" folder

2. **Permission errors**: Ensure you have write permissions in the directory where you're running the script

3. **Memory issues with large PDFs**: The script loads entire PDFs into memory. For very large PDFs, you might need to increase available memory or process them separately

4. **PyPDF2 import error**: Make sure PyPDF2 is installed correctly with `pip install PyPDF2`

## Customization

You can modify the script to change:

- Source directory name (change `source_directory` variable in `main()` function)
- Output directory name (change `output_directory` variable in `main()` function)
- Logging level (change `level=logging.INFO` to `level=logging.DEBUG` for more detailed logs)
- Output filename format (modify the `page_filename` creation in `split_pdf()` function)
